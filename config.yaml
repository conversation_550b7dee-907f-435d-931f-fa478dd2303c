batch:
  create_subdirectory: true
  max_workers: 4
  output_suffix: _centered
  subdirectory_name: centered
  supported_extensions:
  - .cr2
  - .nef
  - .arw
  - .dng
  - .raw
  - .jpg
  - .jpeg
  - .png
  - .tiff
centering:
  chest_weight: 0.3
  crop_aspect_ratio: null
  face_weight: 0.5
  hip_weight: 0.2
  margin_ratio: 0.15
  method: face_chest_based
  target_position:
  - 0.5
  - 0.25
image_processing:
  max_output_size:
  - 3000
  - 3000
  output_format: tiff
  output_quality: 95
  preserve_raw: true
logging:
  backup_count: 3
  file: photo_center.log
  level: INFO
  max_size_mb: 10
models:
  detection_model: auto
  human_detection:
    confidence_threshold: 0.4
    device: cuda
    face_detection_priority: true
    model_path: yolo11x-pose.pt
  openpose:
    confidence_threshold: 0.5
    detection_confidence: 0.5
    graduation_photo_mode: true
    model_complexity: 1
    suppress_warnings: true
    visibility_threshold: 0.5
reference_matching:
  enabled: false
  keypoint_weights:
    eyes: 0.8
    nose: 1.0
    shoulders: 0.6
  similarity_threshold: 0.8
ui:
  auto_refresh: true
  preview_size:
  - 800
  - 600
  show_bounding_box: true
  show_keypoints: true
